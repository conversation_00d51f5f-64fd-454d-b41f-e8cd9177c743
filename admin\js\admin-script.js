// Dakoii Slideshow Admin JavaScript for WordPress Post Type
jQuery(document).ready(function($) {

    var slideIndex = $('.dakoii-slide-item').length;

    // Initialize sortable slides
    if ($('#dakoii-slides-list').length) {
        $('#dakoii-slides-list').sortable({
            handle: '.dakoii-slide-header',
            placeholder: 'dakoii-slide-placeholder',
            update: function(event, ui) {
                updateSlideIndices();
            }
        });
    }

    // Add new slide
    $('#dakoii-add-slide').on('click', function(e) {
        e.preventDefault();

        var template = $('#dakoii-slide-template').html();
        var newSlide = template.replace(/{{INDEX}}/g, slideIndex);

        $('#dakoii-slides-list').append(newSlide);
        slideIndex++;

        // Initialize media uploader for new slide
        initializeMediaUploader();
    });

    // Remove slide
    $(document).on('click', '.dakoii-remove-slide', function(e) {
        e.preventDefault();

        if (confirm('Are you sure you want to remove this slide?')) {
            $(this).closest('.dakoii-slide-item').remove();
            updateSlideIndices();
        }
    });

    // Toggle slide content
    $(document).on('click', '.dakoii-toggle-slide', function(e) {
        e.preventDefault();

        var slideContent = $(this).closest('.dakoii-slide-item').find('.dakoii-slide-content');
        slideContent.slideToggle();
    });

    // Initialize media uploader
    function initializeMediaUploader() {
        $(document).off('click', '.dakoii-upload-image').on('click', '.dakoii-upload-image', function(e) {
            e.preventDefault();

            var button = $(this);
            var container = button.closest('.dakoii-image-upload');
            var imageIdInput = container.find('.dakoii-image-id');
            var imageUrlInput = container.find('.dakoii-image-url');
            var previewContainer = container.find('.dakoii-image-preview');
            var removeButton = container.find('.dakoii-remove-image');

            var mediaUploader = wp.media({
                title: 'Select Slide Image',
                button: {
                    text: 'Use This Image'
                },
                multiple: false
            });

            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();

                imageIdInput.val(attachment.id);
                imageUrlInput.val(attachment.url);
                previewContainer.html('<img src="' + attachment.url + '" style="max-width: 200px; height: auto;" />');
                removeButton.show();

                // Update slide title in header if empty
                var slideItem = button.closest('.dakoii-slide-item');
                var titleInput = slideItem.find('input[name*="[title]"]');
                var slideTitle = slideItem.find('.dakoii-slide-title');

                if (!titleInput.val() && attachment.title) {
                    titleInput.val(attachment.title);
                    slideTitle.text(attachment.title);
                }
            });

            mediaUploader.open();
        });

        // Remove image
        $(document).off('click', '.dakoii-remove-image').on('click', '.dakoii-remove-image', function(e) {
            e.preventDefault();

            var container = $(this).closest('.dakoii-image-upload');
            var imageIdInput = container.find('.dakoii-image-id');
            var imageUrlInput = container.find('.dakoii-image-url');
            var previewContainer = container.find('.dakoii-image-preview');

            imageIdInput.val('');
            imageUrlInput.val('');
            previewContainer.empty();
            $(this).hide();
        });
    }

    // Update slide title in header when title input changes
    $(document).on('input', 'input[name*="[title]"]', function() {
        var slideItem = $(this).closest('.dakoii-slide-item');
        var slideTitle = slideItem.find('.dakoii-slide-title');
        var title = $(this).val() || 'New Slide';
        slideTitle.text(title);
    });

    // Update slide indices after reordering
    function updateSlideIndices() {
        $('#dakoii-slides-list .dakoii-slide-item').each(function(index) {
            $(this).attr('data-index', index);
            $(this).find('input, textarea').each(function() {
                var name = $(this).attr('name');
                if (name) {
                    var newName = name.replace(/\[\d+\]/, '[' + index + ']');
                    $(this).attr('name', newName);
                }
            });
        });
    }

    // Initialize media uploader on page load
    initializeMediaUploader();

    // Copy shortcode to clipboard (for post list page)
    $(document).on('click', '.dakoii-copy-shortcode', function(e) {
        e.preventDefault();

        var shortcode = $(this).data('shortcode');

        // Create temporary input element
        var tempInput = $('<input>');
        $('body').append(tempInput);
        tempInput.val(shortcode).select();
        document.execCommand('copy');
        tempInput.remove();

        // Show feedback
        var originalText = $(this).text();
        $(this).text('Copied!');
        setTimeout(() => {
            $(this).text(originalText);
        }, 2000);
    });

});