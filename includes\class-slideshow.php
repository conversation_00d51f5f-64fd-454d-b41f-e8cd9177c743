<?php
class DakoiiSlideshow {

    public function __construct() {
        add_action('init', array($this, 'register_post_type'));
        add_action('init', array($this, 'register_taxonomy'));
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_slideshow_meta'));
    }

    /**
     * Register the slideshow custom post type
     */
    public function register_post_type() {
        $labels = array(
            'name'                  => _x('Slideshows', 'Post type general name', 'dakoii-slideshow'),
            'singular_name'         => _x('Slideshow', 'Post type singular name', 'dakoii-slideshow'),
            'menu_name'             => _x('Slideshows', 'Admin Menu text', 'dakoii-slideshow'),
            'name_admin_bar'        => _x('Slideshow', 'Add New on Toolbar', 'dakoii-slideshow'),
            'add_new'               => __('Add New', 'dakoii-slideshow'),
            'add_new_item'          => __('Add New Slideshow', 'dakoii-slideshow'),
            'new_item'              => __('New Slideshow', 'dakoii-slideshow'),
            'edit_item'             => __('Edit Slideshow', 'dakoii-slideshow'),
            'view_item'             => __('View Slideshow', 'dakoii-slideshow'),
            'all_items'             => __('All Slideshows', 'dakoii-slideshow'),
            'search_items'          => __('Search Slideshows', 'dakoii-slideshow'),
            'parent_item_colon'     => __('Parent Slideshows:', 'dakoii-slideshow'),
            'not_found'             => __('No slideshows found.', 'dakoii-slideshow'),
            'not_found_in_trash'    => __('No slideshows found in Trash.', 'dakoii-slideshow'),
            'featured_image'        => _x('Slideshow Cover Image', 'Overrides the "Featured Image" phrase', 'dakoii-slideshow'),
            'set_featured_image'    => _x('Set cover image', 'Overrides the "Set featured image" phrase', 'dakoii-slideshow'),
            'remove_featured_image' => _x('Remove cover image', 'Overrides the "Remove featured image" phrase', 'dakoii-slideshow'),
            'use_featured_image'    => _x('Use as cover image', 'Overrides the "Use as featured image" phrase', 'dakoii-slideshow'),
            'archives'              => _x('Slideshow archives', 'The post type archive label', 'dakoii-slideshow'),
            'insert_into_item'      => _x('Insert into slideshow', 'Overrides the "Insert into post" phrase', 'dakoii-slideshow'),
            'uploaded_to_this_item' => _x('Uploaded to this slideshow', 'Overrides the "Uploaded to this post" phrase', 'dakoii-slideshow'),
            'filter_items_list'     => _x('Filter slideshows list', 'Screen reader text for the filter links', 'dakoii-slideshow'),
            'items_list_navigation' => _x('Slideshows list navigation', 'Screen reader text for the pagination', 'dakoii-slideshow'),
            'items_list'            => _x('Slideshows list', 'Screen reader text for the items list', 'dakoii-slideshow'),
        );

        $args = array(
            'labels'             => $labels,
            'public'             => false,
            'publicly_queryable' => false,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'query_var'          => true,
            'rewrite'            => array('slug' => 'slideshow'),
            'capability_type'    => 'post',
            'has_archive'        => false,
            'hierarchical'       => false,
            'menu_position'      => 30,
            'menu_icon'          => 'dashicons-images-alt2',
            'supports'           => array('title', 'editor', 'thumbnail'),
            'show_in_rest'       => true,
        );

        register_post_type('dakoii_slideshow', $args);
    }

    /**
     * Register taxonomy for slideshow tags
     */
    public function register_taxonomy() {
        $labels = array(
            'name'              => _x('Slideshow Tags', 'taxonomy general name', 'dakoii-slideshow'),
            'singular_name'     => _x('Slideshow Tag', 'taxonomy singular name', 'dakoii-slideshow'),
            'search_items'      => __('Search Slideshow Tags', 'dakoii-slideshow'),
            'all_items'         => __('All Slideshow Tags', 'dakoii-slideshow'),
            'parent_item'       => __('Parent Slideshow Tag', 'dakoii-slideshow'),
            'parent_item_colon' => __('Parent Slideshow Tag:', 'dakoii-slideshow'),
            'edit_item'         => __('Edit Slideshow Tag', 'dakoii-slideshow'),
            'update_item'       => __('Update Slideshow Tag', 'dakoii-slideshow'),
            'add_new_item'      => __('Add New Slideshow Tag', 'dakoii-slideshow'),
            'new_item_name'     => __('New Slideshow Tag Name', 'dakoii-slideshow'),
            'menu_name'         => __('Slideshow Tags', 'dakoii-slideshow'),
        );

        $args = array(
            'hierarchical'      => false,
            'labels'            => $labels,
            'show_ui'           => true,
            'show_admin_column' => true,
            'query_var'         => true,
            'rewrite'           => array('slug' => 'slideshow-tag'),
            'show_in_rest'      => true,
        );

        register_taxonomy('dakoii_slideshow_tag', array('dakoii_slideshow'), $args);
    }

    /**
     * Add meta boxes for slideshow settings
     */
    public function add_meta_boxes() {
        add_meta_box(
            'dakoii_slideshow_settings',
            __('Slideshow Settings', 'dakoii-slideshow'),
            array($this, 'slideshow_settings_meta_box'),
            'dakoii_slideshow',
            'normal',
            'high'
        );

        add_meta_box(
            'dakoii_slideshow_slides',
            __('Slides', 'dakoii-slideshow'),
            array($this, 'slideshow_slides_meta_box'),
            'dakoii_slideshow',
            'normal',
            'high'
        );
    }

    /**
     * Slideshow settings meta box callback
     */
    public function slideshow_settings_meta_box($post) {
        wp_nonce_field('dakoii_slideshow_meta_box', 'dakoii_slideshow_meta_box_nonce');

        $autoplay = get_post_meta($post->ID, '_dakoii_autoplay', true);
        $duration = get_post_meta($post->ID, '_dakoii_duration', true);
        $show_nav = get_post_meta($post->ID, '_dakoii_show_nav', true);
        $show_dots = get_post_meta($post->ID, '_dakoii_show_dots', true);

        // Set defaults
        $autoplay = $autoplay !== '' ? $autoplay : '1';
        $duration = $duration !== '' ? $duration : '5000';
        $show_nav = $show_nav !== '' ? $show_nav : '1';
        $show_dots = $show_dots !== '' ? $show_dots : '1';
        ?>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="dakoii_autoplay"><?php _e('Autoplay', 'dakoii-slideshow'); ?></label>
                </th>
                <td>
                    <input type="checkbox" id="dakoii_autoplay" name="dakoii_autoplay" value="1" <?php checked($autoplay, '1'); ?> />
                    <label for="dakoii_autoplay"><?php _e('Enable autoplay', 'dakoii-slideshow'); ?></label>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="dakoii_duration"><?php _e('Duration (ms)', 'dakoii-slideshow'); ?></label>
                </th>
                <td>
                    <input type="number" id="dakoii_duration" name="dakoii_duration" value="<?php echo esc_attr($duration); ?>" min="1000" step="500" />
                    <p class="description"><?php _e('Duration between slides in milliseconds (1000 = 1 second)', 'dakoii-slideshow'); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="dakoii_show_nav"><?php _e('Navigation Arrows', 'dakoii-slideshow'); ?></label>
                </th>
                <td>
                    <input type="checkbox" id="dakoii_show_nav" name="dakoii_show_nav" value="1" <?php checked($show_nav, '1'); ?> />
                    <label for="dakoii_show_nav"><?php _e('Show previous/next navigation arrows', 'dakoii-slideshow'); ?></label>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="dakoii_show_dots"><?php _e('Dot Indicators', 'dakoii-slideshow'); ?></label>
                </th>
                <td>
                    <input type="checkbox" id="dakoii_show_dots" name="dakoii_show_dots" value="1" <?php checked($show_dots, '1'); ?> />
                    <label for="dakoii_show_dots"><?php _e('Show dot indicators', 'dakoii-slideshow'); ?></label>
                </td>
            </tr>
        </table>
        <?php
    }

    /**
     * Slides meta box callback
     */
    public function slideshow_slides_meta_box($post) {
        $slides = get_post_meta($post->ID, '_dakoii_slides', true);
        if (!is_array($slides)) {
            $slides = array();
        }
        ?>
        <div id="dakoii-slides-container">
            <div id="dakoii-slides-list">
                <?php foreach ($slides as $index => $slide): ?>
                    <div class="dakoii-slide-item" data-index="<?php echo $index; ?>">
                        <div class="dakoii-slide-header">
                            <span class="dakoii-slide-title"><?php echo esc_html($slide['title'] ?: 'Slide ' . ($index + 1)); ?></span>
                            <div class="dakoii-slide-actions">
                                <button type="button" class="button dakoii-toggle-slide"><?php _e('Toggle', 'dakoii-slideshow'); ?></button>
                                <button type="button" class="button dakoii-remove-slide"><?php _e('Remove', 'dakoii-slideshow'); ?></button>
                            </div>
                        </div>
                        <div class="dakoii-slide-content">
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label><?php _e('Image', 'dakoii-slideshow'); ?></label>
                                    </th>
                                    <td>
                                        <div class="dakoii-image-upload">
                                            <input type="hidden" name="dakoii_slides[<?php echo $index; ?>][image_id]" value="<?php echo esc_attr($slide['image_id'] ?? ''); ?>" class="dakoii-image-id" />
                                            <input type="hidden" name="dakoii_slides[<?php echo $index; ?>][image_url]" value="<?php echo esc_attr($slide['image_url'] ?? ''); ?>" class="dakoii-image-url" />
                                            <div class="dakoii-image-preview">
                                                <?php if (!empty($slide['image_url'])): ?>
                                                    <img src="<?php echo esc_url($slide['image_url']); ?>" style="max-width: 200px; height: auto;" />
                                                <?php endif; ?>
                                            </div>
                                            <button type="button" class="button dakoii-upload-image"><?php _e('Select Image', 'dakoii-slideshow'); ?></button>
                                            <button type="button" class="button dakoii-remove-image" style="<?php echo empty($slide['image_url']) ? 'display:none;' : ''; ?>"><?php _e('Remove Image', 'dakoii-slideshow'); ?></button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">
                                        <label><?php _e('Title', 'dakoii-slideshow'); ?></label>
                                    </th>
                                    <td>
                                        <input type="text" name="dakoii_slides[<?php echo $index; ?>][title]" value="<?php echo esc_attr($slide['title'] ?? ''); ?>" class="regular-text" />
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">
                                        <label><?php _e('Description', 'dakoii-slideshow'); ?></label>
                                    </th>
                                    <td>
                                        <textarea name="dakoii_slides[<?php echo $index; ?>][description]" rows="3" class="large-text"><?php echo esc_textarea($slide['description'] ?? ''); ?></textarea>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">
                                        <label><?php _e('Link URL', 'dakoii-slideshow'); ?></label>
                                    </th>
                                    <td>
                                        <input type="url" name="dakoii_slides[<?php echo $index; ?>][link_url]" value="<?php echo esc_attr($slide['link_url'] ?? ''); ?>" class="regular-text" />
                                        <p class="description"><?php _e('Optional: URL to link to when slide is clicked', 'dakoii-slideshow'); ?></p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <button type="button" id="dakoii-add-slide" class="button button-primary"><?php _e('Add Slide', 'dakoii-slideshow'); ?></button>
        </div>

        <script type="text/template" id="dakoii-slide-template">
            <div class="dakoii-slide-item" data-index="{{INDEX}}">
                <div class="dakoii-slide-header">
                    <span class="dakoii-slide-title"><?php _e('New Slide', 'dakoii-slideshow'); ?></span>
                    <div class="dakoii-slide-actions">
                        <button type="button" class="button dakoii-toggle-slide"><?php _e('Toggle', 'dakoii-slideshow'); ?></button>
                        <button type="button" class="button dakoii-remove-slide"><?php _e('Remove', 'dakoii-slideshow'); ?></button>
                    </div>
                </div>
                <div class="dakoii-slide-content">
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label><?php _e('Image', 'dakoii-slideshow'); ?></label>
                            </th>
                            <td>
                                <div class="dakoii-image-upload">
                                    <input type="hidden" name="dakoii_slides[{{INDEX}}][image_id]" value="" class="dakoii-image-id" />
                                    <input type="hidden" name="dakoii_slides[{{INDEX}}][image_url]" value="" class="dakoii-image-url" />
                                    <div class="dakoii-image-preview"></div>
                                    <button type="button" class="button dakoii-upload-image"><?php _e('Select Image', 'dakoii-slideshow'); ?></button>
                                    <button type="button" class="button dakoii-remove-image" style="display:none;"><?php _e('Remove Image', 'dakoii-slideshow'); ?></button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label><?php _e('Title', 'dakoii-slideshow'); ?></label>
                            </th>
                            <td>
                                <input type="text" name="dakoii_slides[{{INDEX}}][title]" value="" class="regular-text" />
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label><?php _e('Description', 'dakoii-slideshow'); ?></label>
                            </th>
                            <td>
                                <textarea name="dakoii_slides[{{INDEX}}][description]" rows="3" class="large-text"></textarea>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label><?php _e('Link URL', 'dakoii-slideshow'); ?></label>
                            </th>
                            <td>
                                <input type="url" name="dakoii_slides[{{INDEX}}][link_url]" value="" class="regular-text" />
                                <p class="description"><?php _e('Optional: URL to link to when slide is clicked', 'dakoii-slideshow'); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </script>
        <?php
    }

    /**
     * Save slideshow meta data
     */
    public function save_slideshow_meta($post_id) {
        if (!isset($_POST['dakoii_slideshow_meta_box_nonce'])) {
            return;
        }

        if (!wp_verify_nonce($_POST['dakoii_slideshow_meta_box_nonce'], 'dakoii_slideshow_meta_box')) {
            return;
        }

        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Save slideshow settings
        $autoplay = isset($_POST['dakoii_autoplay']) ? '1' : '0';
        $duration = sanitize_text_field($_POST['dakoii_duration'] ?? '5000');
        $show_nav = isset($_POST['dakoii_show_nav']) ? '1' : '0';
        $show_dots = isset($_POST['dakoii_show_dots']) ? '1' : '0';

        update_post_meta($post_id, '_dakoii_autoplay', $autoplay);
        update_post_meta($post_id, '_dakoii_duration', $duration);
        update_post_meta($post_id, '_dakoii_show_nav', $show_nav);
        update_post_meta($post_id, '_dakoii_show_dots', $show_dots);

        // Save slides
        if (isset($_POST['dakoii_slides']) && is_array($_POST['dakoii_slides'])) {
            $slides = array();
            foreach ($_POST['dakoii_slides'] as $slide_data) {
                if (!empty($slide_data['image_url'])) {
                    $slides[] = array(
                        'image_id' => intval($slide_data['image_id'] ?? 0),
                        'image_url' => esc_url_raw($slide_data['image_url']),
                        'title' => sanitize_text_field($slide_data['title'] ?? ''),
                        'description' => sanitize_textarea_field($slide_data['description'] ?? ''),
                        'link_url' => esc_url_raw($slide_data['link_url'] ?? ''),
                    );
                }
            }
            update_post_meta($post_id, '_dakoii_slides', $slides);
        } else {
            delete_post_meta($post_id, '_dakoii_slides');
        }
    }

    /**
     * Get slideshows by tag (using taxonomy)
     */
    public static function get_slideshows_by_tag($tag) {
        $args = array(
            'post_type' => 'dakoii_slideshow',
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'tax_query' => array(
                array(
                    'taxonomy' => 'dakoii_slideshow_tag',
                    'field'    => 'slug',
                    'terms'    => $tag,
                ),
            ),
        );

        return get_posts($args);
    }

    /**
     * Get slideshow data by post ID
     */
    public static function get_slideshow_data($post_id) {
        $post = get_post($post_id);
        if (!$post || $post->post_type !== 'dakoii_slideshow') {
            return false;
        }

        $slides = get_post_meta($post_id, '_dakoii_slides', true);
        if (!is_array($slides)) {
            $slides = array();
        }

        return array(
            'id' => $post_id,
            'title' => $post->post_title,
            'description' => $post->post_content,
            'slides' => $slides,
            'settings' => array(
                'autoplay' => get_post_meta($post_id, '_dakoii_autoplay', true) === '1',
                'duration' => intval(get_post_meta($post_id, '_dakoii_duration', true) ?: 5000),
                'show_nav' => get_post_meta($post_id, '_dakoii_show_nav', true) === '1',
                'show_dots' => get_post_meta($post_id, '_dakoii_show_dots', true) === '1',
            )
        );
    }
}