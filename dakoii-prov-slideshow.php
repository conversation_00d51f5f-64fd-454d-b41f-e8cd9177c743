<?php
/**
 * Plugin Name: <PERSON><PERSON><PERSON> Prov - Hero Slideshow
 * Plugin URI: https://www.dakoiims.com
 * Description: Create multiple slideshow groups with keyword tags for different pages
 * Version: 1.0.0
 * Author: <PERSON><PERSON>
 * Author URI: https://www.dakoiims.com
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('DAKOII_SLIDESHOW_VERSION', '1.0.0');
define('DAKOII_SLIDESHOW_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('DAKOII_SLIDESHOW_PLUGIN_URL', plugin_dir_url(__FILE__));

// Main plugin class
class DakoiiProvSlideshow {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    public function init() {
        // Load includes
        $this->load_includes();

        // Initialize classes
        new DakoiiSlideshow(); // Initialize post type and meta boxes
        if (is_admin()) {
            new DakoiiSlideshowAdmin();
        }
        new DakoiiSlideshowFrontend();
    }
    
    private function load_includes() {
        require_once DAKOII_SLIDESHOW_PLUGIN_DIR . 'includes/class-slideshow.php';
        require_once DAKOII_SLIDESHOW_PLUGIN_DIR . 'includes/class-admin.php';
        require_once DAKOII_SLIDESHOW_PLUGIN_DIR . 'includes/class-frontend.php';
    }
    
    public function activate() {
        // Load includes first
        $this->load_includes();

        // Initialize slideshow class to register post type
        new DakoiiSlideshow();

        // Flush rewrite rules to register the new post type
        flush_rewrite_rules();
    }

    public function deactivate() {
        // Flush rewrite rules to clean up
        flush_rewrite_rules();
    }
}

// Initialize the plugin
new DakoiiProvSlideshow();