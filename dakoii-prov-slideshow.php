<?php
/**
 * Plugin Name: Da<PERSON><PERSON> Prov - Hero Slideshow
 * Plugin URI: https://www.dakoiims.com
 * Description: Create multiple slideshow groups with keyword tags for different pages
 * Version: 1.0.0
 * Author: <PERSON><PERSON>
 * Author URI: https://www.dakoiims.com
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('DAKOII_SLIDESHOW_VERSION', '1.0.0');
define('DAKOII_SLIDESHOW_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('DAKOII_SLIDESHOW_PLUGIN_URL', plugin_dir_url(__FILE__));

// Main plugin class
class DakoiiProvSlideshow {
    
    public function __construct() {
        add_action('init', array($this, 'init'), 0); // Higher priority
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));

        // Debug: Add admin notice to check if plugin is loading
        add_action('admin_notices', array($this, 'debug_admin_notice'));
    }
    
    public function init() {
        // Debug: Log that init is being called
        error_log('Dakoii Slideshow: Init method called');

        // Load includes
        $this->load_includes();

        // Initialize classes
        new DakoiiSlideshow(); // Initialize post type and meta boxes
        if (is_admin()) {
            new DakoiiSlideshowAdmin();
        }
        new DakoiiSlideshowFrontend();

        // Debug: Log that initialization is complete
        error_log('Dakoii Slideshow: Initialization complete');
    }
    
    private function load_includes() {
        $slideshow_file = DAKOII_SLIDESHOW_PLUGIN_DIR . 'includes/class-slideshow.php';
        $admin_file = DAKOII_SLIDESHOW_PLUGIN_DIR . 'includes/class-admin.php';
        $frontend_file = DAKOII_SLIDESHOW_PLUGIN_DIR . 'includes/class-frontend.php';

        // Debug: Check if files exist
        error_log('Dakoii Slideshow: Loading files...');
        error_log('Slideshow file exists: ' . (file_exists($slideshow_file) ? 'YES' : 'NO'));
        error_log('Admin file exists: ' . (file_exists($admin_file) ? 'YES' : 'NO'));
        error_log('Frontend file exists: ' . (file_exists($frontend_file) ? 'YES' : 'NO'));

        require_once $slideshow_file;
        require_once $admin_file;
        require_once $frontend_file;

        error_log('Dakoii Slideshow: Files loaded successfully');
    }
    
    public function activate() {
        // Load includes first
        $this->load_includes();

        // Initialize slideshow class to register post type
        new DakoiiSlideshow();

        // Flush rewrite rules to register the new post type
        flush_rewrite_rules();
    }

    public function deactivate() {
        // Flush rewrite rules to clean up
        flush_rewrite_rules();
    }

    /**
     * Debug admin notice to check if plugin is loading
     */
    public function debug_admin_notice() {
        if (current_user_can('manage_options')) {
            $post_types = get_post_types(array('public' => false, 'show_ui' => true), 'objects');
            $slideshow_registered = isset($post_types['dakoii_slideshow']);

            echo '<div class="notice notice-info is-dismissible">';
            echo '<p><strong>Dakoii Slideshow Debug:</strong> Plugin loaded. Post type registered: ' . ($slideshow_registered ? 'YES' : 'NO') . '</p>';
            if ($slideshow_registered) {
                echo '<p>Post type details: ' . $post_types['dakoii_slideshow']->labels->name . '</p>';
            }
            echo '</div>';
        }
    }
}

// Initialize the plugin
new DakoiiProvSlideshow();