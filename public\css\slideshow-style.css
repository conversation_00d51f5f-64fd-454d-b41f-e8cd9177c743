.dakoii-slideshow {
    position: relative;
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.slideshow-container {
    position: relative;
    width: 100%;
    height: 400px; /* Default height, can be customized */
    overflow: hidden;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.slide.active {
    opacity: 1;
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slide-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 40px 30px 30px;
    text-align: left;
}

.slide-content h3 {
    font-size: 2rem;
    margin: 0 0 10px 0;
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.slide-content p {
    font-size: 1.1rem;
    margin: 0;
    line-height: 1.4;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Navigation Buttons */
.slideshow-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    border: none;
    color: #333;
    font-size: 24px;
    font-weight: bold;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.slideshow-nav:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-50%) scale(1.1);
}

.slideshow-nav.prev {
    left: 20px !important;
    right: auto !important;
}

.slideshow-nav.next {
    right: 20px !important;
    left: auto !important;
}

/* Dots Navigation */
.slideshow-dots {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 10;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.7);
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot:hover,
.dot.active {
    background: white;
    border-color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .slideshow-container {
        height: 250px;
    }
    
    .slide-content {
        padding: 20px 15px 15px;
    }
    
    .slide-content h3 {
        font-size: 1.5rem;
        margin-bottom: 8px;
    }
    
    .slide-content p {
        font-size: 1rem;
    }
    
    .slideshow-nav {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }
    
    .slideshow-nav.prev {
        left: 10px !important;
        right: auto !important;
    }

    .slideshow-nav.next {
        right: 10px !important;
        left: auto !important;
    }
    
    .slideshow-dots {
        bottom: 15px;
    }
    
    .dot {
        width: 10px;
        height: 10px;
    }
}

@media (max-width: 480px) {
    .slideshow-container {
        height: 200px;
    }
    
    .slide-content {
        padding: 15px 10px 10px;
    }
    
    .slide-content h3 {
        font-size: 1.2rem;
        margin-bottom: 5px;
    }
    
    .slide-content p {
        font-size: 0.9rem;
    }
    
    .slideshow-nav {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }
    
    .slideshow-nav.prev {
        left: 5px !important;
        right: auto !important;
    }

    .slideshow-nav.next {
        right: 5px !important;
        left: auto !important;
    }
}

/* Custom Height Classes */
.dakoii-slideshow.height-small .slideshow-container {
    height: 250px;
}

.dakoii-slideshow.height-medium .slideshow-container {
    height: 400px;
}

.dakoii-slideshow.height-large .slideshow-container {
    height: 600px;
}

.dakoii-slideshow.height-extra-large .slideshow-container {
    height: 800px;
}

/* Full width variant */
.dakoii-slideshow.full-width {
    border-radius: 0;
    box-shadow: none;
}

/* Fade transition variant */
.dakoii-slideshow.fade-transition .slide {
    transition: opacity 0.8s ease-in-out;
}

/* No overlay variant */
.dakoii-slideshow.no-overlay .slide-content {
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
}

/* Light text variant */
.dakoii-slideshow.light-text .slide-content {
    color: #333;
    background: linear-gradient(transparent, rgba(255, 255, 255, 0.9));
}

.dakoii-slideshow.light-text .slide-content h3,
.dakoii-slideshow.light-text .slide-content p {
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

/* Accessibility improvements */
.slideshow-nav:focus,
.dot:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

/* Loading state */
.dakoii-slideshow.loading {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
}

.dakoii-slideshow.loading::before {
    content: "Loading slideshow...";
    color: #666;
    font-style: italic;
}