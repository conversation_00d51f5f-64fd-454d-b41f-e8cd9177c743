.dakoii-slideshow-admin {
    max-width: 1200px;
}

#edit-group-modal,
#manage-slides-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 100000;
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 20px;
    border-radius: 5px;
    max-width: 600px;
    width: 90%;
    max-height: 90%;
    overflow-y: auto;
}

.modal-content.large {
    max-width: 800px;
}

.slide-item {
    border: 1px solid #ddd;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 5px;
    background: #f9f9f9;
}

.slide-preview {
    display: flex;
    align-items: center;
    gap: 15px;
}

.slide-preview img {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 3px;
}

.slide-info {
    flex: 1;
}

.slide-actions {
    display: flex;
    gap: 10px;
}

.notice {
    margin: 15px 0;
}

.shortcode-display {
    background: #f1f1f1;
    padding: 5px 10px;
    border-radius: 3px;
    font-family: monospace;
}

/* Modal styling improvements */
.modal-content h3 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.modal-content .form-table {
    margin-top: 20px;
}

.modal-content .submit {
    border-top: 1px solid #ddd;
    padding-top: 15px;
    margin-top: 20px;
}

/* Slide management specific styles */
#slides-container {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.slide-item .slide-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.slide-item .slide-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
}

.slide-item .slide-order {
    color: #999;
    font-size: 12px;
}

/* Button styling */
.slide-actions .button {
    margin-right: 5px;
}

.slide-actions .delete-slide {
    color: #a00;
}

.slide-actions .delete-slide:hover {
    color: #dc3232;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        padding: 15px;
    }
    
    .slide-preview {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .slide-preview img {
        width: 100%;
        height: auto;
        max-height: 150px;
    }
}