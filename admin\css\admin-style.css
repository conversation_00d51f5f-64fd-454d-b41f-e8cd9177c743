/* Da<PERSON><PERSON> Slideshow Admin Styles for WordPress Post Type */

/* Meta Box Styles */
.dakoii-slide-item {
    border: 1px solid #ddd;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 4px;
}

.dakoii-slide-header {
    background: #f9f9f9;
    padding: 12px 15px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: move;
}

.dakoii-slide-title {
    font-weight: 600;
    color: #23282d;
}

.dakoii-slide-actions {
    display: flex;
    gap: 8px;
}

.dakoii-slide-actions .button {
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
    line-height: 1.4;
}

.dakoii-slide-content {
    padding: 15px;
    display: none;
}

.dakoii-slide-content.active {
    display: block;
}

.dakoii-slide-content .form-table {
    margin: 0;
}

.dakoii-slide-content .form-table th {
    width: 150px;
    padding: 10px 0;
}

.dakoii-slide-content .form-table td {
    padding: 10px 0;
}

/* Image Upload Styles */
.dakoii-image-upload {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.dakoii-image-preview {
    border: 2px dashed #ddd;
    padding: 20px;
    text-align: center;
    border-radius: 4px;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dakoii-image-preview img {
    max-width: 200px;
    height: auto;
    border-radius: 4px;
}

.dakoii-image-preview:empty::before {
    content: "No image selected";
    color: #999;
    font-style: italic;
}

.dakoii-upload-image,
.dakoii-remove-image {
    align-self: flex-start;
}

/* Sortable Styles */
#dakoii-slides-list.ui-sortable .dakoii-slide-item {
    cursor: move;
}

.dakoii-slide-placeholder {
    border: 2px dashed #0073aa;
    background: #f0f8ff;
    height: 60px;
    margin-bottom: 15px;
    border-radius: 4px;
}

/* Add Slide Button */
#dakoii-add-slide {
    margin-top: 15px;
}

/* Settings Meta Box */
#dakoii_slideshow_settings .form-table th {
    width: 200px;
}

#dakoii_slideshow_settings .form-table td {
    padding: 15px 10px;
}

#dakoii_slideshow_settings input[type="number"] {
    width: 100px;
}

#dakoii_slideshow_settings .description {
    margin-top: 5px;
    font-style: italic;
    color: #666;
}

/* Copy Shortcode Button */
.dakoii-copy-shortcode {
    background: #f0f0f1;
    border: 1px solid #c3c4c7;
    color: #2c3338;
    cursor: pointer;
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 3px;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-block;
}

.dakoii-copy-shortcode:hover {
    background: #e0e0e0;
    border-color: #8c8f94;
    color: #2c3338;
}

/* Post List Columns */
.column-shortcode {
    width: 200px;
}

.column-slides {
    width: 80px;
    text-align: center;
}

.column-settings {
    width: 150px;
}

/* Responsive */
@media (max-width: 768px) {
    .dakoii-slide-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .dakoii-slide-actions {
        align-self: flex-end;
    }

    .dakoii-image-preview {
        min-height: 80px;
    }

    #dakoii_slideshow_settings .form-table th,
    #dakoii_slideshow_settings .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }

    #dakoii_slideshow_settings .form-table th {
        padding-bottom: 5px;
    }
}

/* Loading States */
.dakoii-loading {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
}

/* Error States */
.dakoii-error {
    background: #fff2f2;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 10px 15px;
    border-radius: 4px;
    margin: 10px 0;
}

/* Success States */
.dakoii-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 10px 15px;
    border-radius: 4px;
    margin: 10px 0;
}