<?php
class DakoiiSlideshowAdmin {
    
    public function __construct() {
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_filter('manage_dakoii_slideshow_posts_columns', array($this, 'add_custom_columns'));
        add_action('manage_dakoii_slideshow_posts_custom_column', array($this, 'custom_column_content'), 10, 2);
        add_action('admin_footer', array($this, 'add_admin_footer_script'));
    }
    
    public function enqueue_admin_scripts($hook) {
        // Only load on slideshow post type pages
        if ($hook === 'post.php' || $hook === 'post-new.php' || $hook === 'edit.php') {
            global $post_type;
            if ($post_type === 'dakoii_slideshow') {
                wp_enqueue_media();
                wp_enqueue_script('jquery-ui-sortable');
                wp_enqueue_script(
                    'dakoii-admin-js',
                    DAKOII_SLIDESHOW_PLUGIN_URL . 'admin/js/admin-script.js',
                    array('jquery', 'jquery-ui-sortable'),
                    DAKOII_SLIDESHOW_VERSION
                );
                wp_enqueue_style(
                    'dakoii-admin-css',
                    DAKOII_SLIDESHOW_PLUGIN_URL . 'admin/css/admin-style.css',
                    array(),
                    DAKOII_SLIDESHOW_VERSION
                );
            }
        }
    }
    
    /**
     * Add custom columns to slideshow post list
     */
    public function add_custom_columns($columns) {
        $new_columns = array();
        $new_columns['cb'] = $columns['cb'];
        $new_columns['title'] = $columns['title'];
        $new_columns['shortcode'] = __('Shortcode', 'dakoii-slideshow');
        $new_columns['slides'] = __('Slides', 'dakoii-slideshow');
        $new_columns['settings'] = __('Settings', 'dakoii-slideshow');
        $new_columns['dakoii_slideshow_tag'] = __('Tags', 'dakoii-slideshow');
        $new_columns['date'] = $columns['date'];
        
        return $new_columns;
    }
    
    /**
     * Display custom column content
     */
    public function custom_column_content($column, $post_id) {
        switch ($column) {
            case 'shortcode':
                $shortcode_id = '[dakoii_slideshow id="' . $post_id . '"]';
                $shortcode_slug = '[dakoii_slideshow slug="' . get_post_field('post_name', $post_id) . '"]';
                echo '<div class="shortcode-display">';
                echo '<div style="margin-bottom: 5px;">';
                echo '<code>' . esc_html($shortcode_id) . '</code>';
                echo ' <button type="button" class="dakoii-copy-shortcode button button-small" data-shortcode="' . esc_attr($shortcode_id) . '">Copy</button>';
                echo '</div>';
                echo '<div>';
                echo '<code>' . esc_html($shortcode_slug) . '</code>';
                echo ' <button type="button" class="dakoii-copy-shortcode button button-small" data-shortcode="' . esc_attr($shortcode_slug) . '">Copy</button>';
                echo '</div>';
                echo '</div>';
                break;
                
            case 'slides':
                $slides = get_post_meta($post_id, '_dakoii_slides', true);
                $count = is_array($slides) ? count($slides) : 0;
                echo '<span class="slides-count">' . $count . '</span>';
                break;
                
            case 'settings':
                $autoplay = get_post_meta($post_id, '_dakoii_autoplay', true) === '1';
                $duration = get_post_meta($post_id, '_dakoii_duration', true) ?: '5000';
                $show_nav = get_post_meta($post_id, '_dakoii_show_nav', true) === '1';
                $show_dots = get_post_meta($post_id, '_dakoii_show_dots', true) === '1';
                
                echo '<div class="settings-summary">';
                echo '<div>Autoplay: ' . ($autoplay ? 'Yes' : 'No') . '</div>';
                echo '<div>Duration: ' . esc_html($duration) . 'ms</div>';
                echo '<div>Nav: ' . ($show_nav ? 'Yes' : 'No') . ' | Dots: ' . ($show_dots ? 'Yes' : 'No') . '</div>';
                echo '</div>';
                break;
        }
    }
    
    /**
     * Add admin footer script for copy functionality
     */
    public function add_admin_footer_script() {
        global $post_type;
        if ($post_type === 'dakoii_slideshow') {
            ?>
            <script type="text/javascript">
            jQuery(document).ready(function($) {
                // Copy shortcode functionality
                $(document).on('click', '.dakoii-copy-shortcode', function(e) {
                    e.preventDefault();
                    
                    var shortcode = $(this).data('shortcode');
                    
                    // Create temporary input element
                    var tempInput = $('<input>');
                    $('body').append(tempInput);
                    tempInput.val(shortcode).select();
                    document.execCommand('copy');
                    tempInput.remove();
                    
                    // Show feedback
                    var originalText = $(this).text();
                    $(this).text('Copied!');
                    setTimeout(function() {
                        $(this).text(originalText);
                    }.bind(this), 2000);
                });
            });
            </script>
            <?php
        }
    }
}
